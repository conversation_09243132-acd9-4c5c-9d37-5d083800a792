const socket = io();
let userInfo = {};
let isTyping = false;
let typingTimeout;

// DOM Elements
const userSetup = document.getElementById('userSetup');
const chatMain = document.getElementById('chatMain');
const usernameInput = document.getElementById('usernameInput');
const ageInput = document.getElementById('ageInput');
const genderSelect = document.getElementById('genderSelect');
const countrySelect = document.getElementById('countrySelect');
const joinBtn = document.getElementById('joinBtn');
const messageInput = document.getElementById('messageInput');
const sendBtn = document.getElementById('sendBtn');
const messagesContainer = document.getElementById('messagesContainer');
const status = document.getElementById('status');
const typingIndicator = document.getElementById('typingIndicator');

// Countries list
const countries = [
    'Afghanistan', 'Albania', 'Algeria', 'Argentina', 'Armenia', 'Australia',
    'Austria', 'Azerbaijan', 'Bahrain', 'Bangladesh', 'Belarus', 'Belgium',
    'Bolivia', 'Bosnia and Herzegovina', 'Brazil', 'Bulgaria', 'Cambodia',
    'Canada', 'Chile', 'China', 'Colombia', 'Croatia', 'Czech Republic',
    'Denmark', 'Ecuador', 'Egypt', 'Estonia', 'Finland', 'France', 'Georgia',
    'Germany', 'Ghana', 'Greece', 'Hungary', 'Iceland', 'India', 'Indonesia',
    'Iran', 'Iraq', 'Ireland', 'Israel', 'Italy', 'Japan', 'Jordan',
    'Kazakhstan', 'Kenya', 'Kuwait', 'Latvia', 'Lebanon', 'Lithuania',
    'Luxembourg', 'Malaysia', 'Mexico', 'Morocco', 'Netherlands', 'New Zealand',
    'Nigeria', 'Norway', 'Pakistan', 'Palestine', 'Peru', 'Philippines',
    'Poland', 'Portugal', 'Qatar', 'Romania', 'Russia', 'Saudi Arabia',
    'Singapore', 'Slovakia', 'Slovenia', 'South Africa', 'South Korea',
    'Spain', 'Sri Lanka', 'Sweden', 'Switzerland', 'Syria', 'Thailand',
    'Tunisia', 'Turkey', 'Ukraine', 'United Arab Emirates', 'United Kingdom',
    'United States', 'Uruguay', 'Venezuela', 'Vietnam', 'Yemen'
];

// Initialize countries dropdown
function initializeCountries() {
    countries.forEach(country => {
        const option = document.createElement('option');
        option.value = country;
        option.textContent = country;
        countrySelect.appendChild(option);
    });
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', initializeCountries);

// Event Listeners
joinBtn.addEventListener('click', joinChat);
usernameInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') joinChat();
});

sendBtn.addEventListener('click', sendMessage);
messageInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') sendMessage();
});

messageInput.addEventListener('input', handleTyping);

// Functions
function validateForm() {
    const name = usernameInput.value.trim();
    const age = ageInput.value;
    const gender = genderSelect.value;
    const country = countrySelect.value;

    if (!name || !age || !gender || !country) {
        alert('Please fill in all fields');
        return false;
    }

    if (age < 13 || age > 100) {
        alert('Age must be between 13 and 100');
        return false;
    }

    return true;
}

function joinChat() {
    if (!validateForm()) return;

    userInfo = {
        username: usernameInput.value.trim(),
        age: parseInt(ageInput.value),
        gender: genderSelect.value,
        country: countrySelect.value
    };

    socket.emit('join', userInfo);
    userSetup.style.display = 'none';
    chatMain.style.display = 'flex';
    messageInput.focus();
}

function sendMessage() {
    const message = messageInput.value.trim();
    if (message && userInfo.username) {
        socket.emit('message', {
            userInfo: userInfo,
            text: message,
            timestamp: new Date()
        });
        messageInput.value = '';
        stopTyping();
    }
}

function handleTyping() {
    if (!isTyping) {
        isTyping = true;
        socket.emit('typing', { userInfo, typing: true });
    }

    clearTimeout(typingTimeout);
    typingTimeout = setTimeout(() => {
        stopTyping();
    }, 1000);
}

function stopTyping() {
    if (isTyping) {
        isTyping = false;
        socket.emit('typing', { userInfo, typing: false });
    }
}

function addMessage(data, isOwn = false) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${isOwn ? 'own' : ''}`;

    const time = new Date(data.timestamp).toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit'
    });

    const userDisplayInfo = isOwn ? 'You' :
        `${data.userInfo.username} (${data.userInfo.age}, ${data.userInfo.gender}, ${data.userInfo.country})`;

    messageDiv.innerHTML = `
        <div class="message-content">
            ${data.text}
        </div>
        <div class="message-info">
            ${userDisplayInfo} • ${time}
        </div>
    `;

    messagesContainer.appendChild(messageDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function addSystemMessage(text) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'user-joined';
    messageDiv.textContent = text;
    messagesContainer.appendChild(messageDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// Socket Events
socket.on('connect', () => {
    status.textContent = 'Connected';
    status.classList.add('connected');
});

socket.on('disconnect', () => {
    status.textContent = 'Disconnected';
    status.classList.remove('connected');
});

socket.on('message', (data) => {
    const isOwn = data.userInfo.username === userInfo.username;
    addMessage(data, isOwn);
});

socket.on('user-joined', (data) => {
    if (data.userInfo.username !== userInfo.username) {
        addSystemMessage(`${data.userInfo.username} joined the chat`);
    }
});

socket.on('user-left', (data) => {
    addSystemMessage(`${data.userInfo.username} left the chat`);
});

socket.on('typing', (data) => {
    if (data.userInfo.username !== userInfo.username) {
        if (data.typing) {
            typingIndicator.innerHTML = `<span>${data.userInfo.username}</span> is typing...`;
            typingIndicator.style.display = 'block';
        } else {
            typingIndicator.style.display = 'none';
        }
    }
});

socket.on('users-count', (count) => {
    if (count > 1) {
        status.textContent = `Connected (${count} users)`;
    } else {
        status.textContent = 'Connected (waiting for other users)';
    }
});
