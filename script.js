const socket = io();
let userInfo = {};
let isTyping = false;
let typingTimeout;

// DOM Elements
const userSetup = document.getElementById('userSetup');
const chatMain = document.getElementById('chatMain');
const usernameInput = document.getElementById('usernameInput');
const ageInput = document.getElementById('ageInput');
const genderSelect = document.getElementById('genderSelect');
const countrySelect = document.getElementById('countrySelect');
const joinBtn = document.getElementById('joinBtn');
const messageInput = document.getElementById('messageInput');
const sendBtn = document.getElementById('sendBtn');
const messagesContainer = document.getElementById('messagesContainer');
const status = document.getElementById('status');
const typingIndicator = document.getElementById('typingIndicator');

// Countries list
const countries = [
    'Afghanistan', 'Albania', 'Algeria', 'Argentina', 'Armenia', 'Australia',
    'Austria', 'Azerbaijan', 'Bahrain', 'Bangladesh', 'Belarus', 'Belgium',
    'Bolivia', 'Bosnia and Herzegovina', 'Brazil', 'Bulgaria', 'Cambodia',
    'Canada', 'Chile', 'China', 'Colombia', 'Croatia', 'Czech Republic',
    'Denmark', 'Ecuador', 'Egypt', 'Estonia', 'Finland', 'France', 'Georgia',
    'Germany', 'Ghana', 'Greece', 'Hungary', 'Iceland', 'India', 'Indonesia',
    'Iran', 'Iraq', 'Ireland', 'Israel', 'Italy', 'Japan', 'Jordan',
    'Kazakhstan', 'Kenya', 'Kuwait', 'Latvia', 'Lebanon', 'Lithuania',
    'Luxembourg', 'Malaysia', 'Mexico', 'Morocco', 'Netherlands', 'New Zealand',
    'Nigeria', 'Norway', 'Pakistan', 'Palestine', 'Peru', 'Philippines',
    'Poland', 'Portugal', 'Qatar', 'Romania', 'Russia', 'Saudi Arabia',
    'Singapore', 'Slovakia', 'Slovenia', 'South Africa', 'South Korea',
    'Spain', 'Sri Lanka', 'Sweden', 'Switzerland', 'Syria', 'Thailand',
    'Tunisia', 'Turkey', 'Ukraine', 'United Arab Emirates', 'United Kingdom',
    'United States', 'Uruguay', 'Venezuela', 'Vietnam', 'Yemen'
];

// Initialize countries dropdown
function initializeCountries() {
    try {
        countries.forEach(country => {
            const option = document.createElement('option');
            option.value = country;
            option.textContent = country;
            countrySelect.appendChild(option);
        });
        console.log('Countries initialized successfully');
    } catch (error) {
        console.error('Error initializing countries:', error);
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', initializeCountries);

// Event Listeners
joinBtn.addEventListener('click', joinChat);

// Add Enter key support for all form fields
[usernameInput, ageInput, genderSelect, countrySelect].forEach(field => {
    field.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            joinChat();
        }
    });
});

sendBtn.addEventListener('click', sendMessage);
messageInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
        e.preventDefault();
        sendMessage();
    }
});

messageInput.addEventListener('input', handleTyping);

// Functions
function validateForm() {
    const name = usernameInput.value.trim();
    const age = ageInput.value;
    const gender = genderSelect.value;
    const country = countrySelect.value;

    console.log('Validating form:', { name, age, gender, country }); // Debug log

    if (!name) {
        alert('Please enter your name');
        usernameInput.focus();
        return false;
    }

    if (!age) {
        alert('Please enter your age');
        ageInput.focus();
        return false;
    }

    if (!gender) {
        alert('Please select your gender');
        genderSelect.focus();
        return false;
    }

    if (!country) {
        alert('Please select your country');
        countrySelect.focus();
        return false;
    }

    const ageNum = parseInt(age);
    if (isNaN(ageNum) || ageNum < 13 || ageNum > 100) {
        alert('Age must be between 13 and 100');
        ageInput.focus();
        return false;
    }

    return true;
}

function joinChat() {
    console.log('Join chat button clicked'); // Debug log

    // Disable button to prevent multiple clicks
    joinBtn.disabled = true;
    joinBtn.textContent = 'Joining...';

    if (!validateForm()) {
        console.log('Form validation failed'); // Debug log
        // Re-enable button
        joinBtn.disabled = false;
        joinBtn.textContent = 'Join Chat';
        return;
    }

    userInfo = {
        username: usernameInput.value.trim(),
        age: parseInt(ageInput.value),
        gender: genderSelect.value,
        country: countrySelect.value
    };

    console.log('User info created:', userInfo); // Debug log

    try {
        socket.emit('join', userInfo);

        // Wait a moment then switch views
        setTimeout(() => {
            userSetup.style.display = 'none';
            chatMain.style.display = 'flex';
            messageInput.focus();
            console.log('Successfully joined chat'); // Debug log
        }, 500);

    } catch (error) {
        console.error('Error joining chat:', error);
        alert('Error joining chat. Please try again.');
        // Re-enable button
        joinBtn.disabled = false;
        joinBtn.textContent = 'Join Chat';
    }
}

function sendMessage() {
    const message = messageInput.value.trim();
    if (message && userInfo.username) {
        socket.emit('message', {
            userInfo: userInfo,
            text: message,
            timestamp: new Date()
        });
        messageInput.value = '';
        stopTyping();
    }
}

function handleTyping() {
    if (!isTyping) {
        isTyping = true;
        socket.emit('typing', { userInfo, typing: true });
    }

    clearTimeout(typingTimeout);
    typingTimeout = setTimeout(() => {
        stopTyping();
    }, 1000);
}

function stopTyping() {
    if (isTyping) {
        isTyping = false;
        socket.emit('typing', { userInfo, typing: false });
    }
}

function addMessage(data, isOwn = false) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${isOwn ? 'own' : ''}`;

    const time = new Date(data.timestamp).toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit'
    });

    const userDisplayInfo = isOwn ? 'You' :
        `${data.userInfo.username} (${data.userInfo.age}, ${data.userInfo.gender}, ${data.userInfo.country})`;

    messageDiv.innerHTML = `
        <div class="message-content">
            ${data.text}
        </div>
        <div class="message-info">
            ${userDisplayInfo} • ${time}
        </div>
    `;

    messagesContainer.appendChild(messageDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function addSystemMessage(text) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'user-joined';
    messageDiv.textContent = text;
    messagesContainer.appendChild(messageDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// Socket Events
socket.on('connect', () => {
    console.log('Connected to server');
    status.textContent = 'Connected';
    status.classList.add('connected');
});

socket.on('disconnect', () => {
    console.log('Disconnected from server');
    status.textContent = 'Disconnected';
    status.classList.remove('connected');
});

socket.on('connect_error', (error) => {
    console.error('Connection error:', error);
    status.textContent = 'Connection Error';
    status.classList.remove('connected');
});

socket.on('message', (data) => {
    const isOwn = data.userInfo.username === userInfo.username;
    addMessage(data, isOwn);
});

socket.on('user-joined', (data) => {
    if (userInfo && userInfo.username && data.userInfo.username !== userInfo.username) {
        addSystemMessage(`${data.userInfo.username} joined the chat`);
    }
});

socket.on('user-left', (data) => {
    addSystemMessage(`${data.userInfo.username} left the chat`);
});

socket.on('typing', (data) => {
    if (userInfo && userInfo.username && data.userInfo.username !== userInfo.username) {
        if (data.typing) {
            typingIndicator.innerHTML = `<span>${data.userInfo.username}</span> is typing...`;
            typingIndicator.style.display = 'block';
        } else {
            typingIndicator.style.display = 'none';
        }
    }
});

socket.on('users-count', (count) => {
    if (count > 1) {
        status.textContent = `Connected (${count} users)`;
    } else {
        status.textContent = 'Connected (waiting for other users)';
    }
});
