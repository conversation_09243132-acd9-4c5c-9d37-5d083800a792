const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');

const app = express();
const server = http.createServer(app);
const io = socketIo(server);

// Serve static files
app.use(express.static(path.join(__dirname)));

// Route for main page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Store connected users
const users = new Map();

// Socket.io connection handling
io.on('connection', (socket) => {
    console.log('New user connected:', socket.id);

    // Handle user joining
    socket.on('join', (userInfo) => {
        users.set(socket.id, {
            userInfo: userInfo,
            socketId: socket.id
        });

        console.log(`${userInfo.username} (${userInfo.age}, ${userInfo.gender}, ${userInfo.country}) joined the chat`);

        // Notify all users about new user
        socket.broadcast.emit('user-joined', { userInfo });

        // Send current users count to all clients
        io.emit('users-count', users.size);
    });

    // Handle messages
    socket.on('message', (data) => {
        const user = users.get(socket.id);
        if (user) {
            console.log(`Message from ${user.userInfo.username}: ${data.text}`);

            // Broadcast message to all clients
            io.emit('message', {
                userInfo: data.userInfo,
                text: data.text,
                timestamp: data.timestamp
            });
        }
    });

    // Handle typing indicator
    socket.on('typing', (data) => {
        socket.broadcast.emit('typing', data);
    });

    // Handle disconnection
    socket.on('disconnect', () => {
        const user = users.get(socket.id);
        if (user) {
            console.log(`${user.userInfo.username} left the chat`);

            // Notify all users about user leaving
            socket.broadcast.emit('user-left', { userInfo: user.userInfo });

            // Remove user from users map
            users.delete(socket.id);

            // Send updated users count
            io.emit('users-count', users.size);
        }
    });
});

const PORT = process.env.PORT || 3000;

server.listen(PORT, () => {
    console.log(`🚀 Server running on port ${PORT}`);
    console.log(`🌐 Open browser at: http://localhost:${PORT}`);
});
