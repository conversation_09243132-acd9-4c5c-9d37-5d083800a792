<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Text Chat</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="chat-container">
        <header class="chat-header">
            <h1>💬 Text Chat</h1>
            <div class="status" id="status">Disconnected</div>
        </header>

        <div class="user-setup" id="userSetup">
            <div class="setup-card">
                <h2>Welcome!</h2>
                <p>Please fill in your information to start chatting</p>

                <div class="form-group">
                    <label for="usernameInput">Name</label>
                    <input type="text" id="usernameInput" placeholder="Your name..." maxlength="20" required>
                </div>

                <div class="form-group">
                    <label for="ageInput">Age</label>
                    <input type="number" id="ageInput" placeholder="Your age..." min="13" max="100" required>
                </div>

                <div class="form-group">
                    <label for="genderSelect">Gender</label>
                    <select id="genderSelect" required>
                        <option value="">Select gender...</option>
                        <option value="male">Male</option>
                        <option value="female">Female</option>
                        <option value="other">Other</option>
                        <option value="prefer-not-to-say">Prefer not to say</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="countrySelect">Country</label>
                    <select id="countrySelect" required>
                        <option value="">Select country...</option>
                    </select>
                </div>

                <button id="joinBtn">Join Chat</button>
            </div>
        </div>

        <div class="chat-main" id="chatMain" style="display: none;">
            <div class="messages-container" id="messagesContainer">
                <div class="welcome-message">
                    <p>Welcome! You can now start chatting</p>
                </div>
            </div>

            <div class="message-input-container">
                <div class="input-wrapper">
                    <input type="text" id="messageInput" placeholder="Type your message here..." maxlength="500">
                    <button id="sendBtn">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M2 21L23 12L2 3V10L17 12L2 14V21Z" fill="currentColor"/>
                        </svg>
                    </button>
                </div>
                <div class="typing-indicator" id="typingIndicator" style="display: none;">
                    <span></span> is typing...
                </div>
            </div>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script src="script.js"></script>
</body>
</html>
