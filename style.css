* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.chat-container {
    width: 100%;
    max-width: 800px;
    height: 600px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.chat-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    text-align: center;
    position: relative;
}

.chat-header h1 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 5px;
}

.status {
    font-size: 14px;
    opacity: 0.9;
    padding: 5px 15px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    display: inline-block;
}

.status.connected {
    background: rgba(76, 175, 80, 0.8);
}

.user-setup {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
}

.setup-card {
    text-align: center;
    background: #f8f9fa;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.setup-card h2 {
    color: #333;
    margin-bottom: 10px;
    font-size: 28px;
}

.setup-card p {
    color: #666;
    margin-bottom: 30px;
    font-size: 16px;
}

.form-group {
    margin-bottom: 20px;
    text-align: left;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 500;
    font-size: 14px;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 16px;
    font-family: 'Inter', sans-serif;
    background: white;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
}

.form-group select {
    cursor: pointer;
}

#joinBtn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    transition: all 0.3s ease;
    width: 100%;
    margin-top: 10px;
    position: relative;
    overflow: hidden;
}

#joinBtn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

#joinBtn:active {
    transform: translateY(0);
}

#joinBtn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.form-group input:invalid,
.form-group select:invalid {
    border-color: #ff6b6b;
}

.form-group input:valid,
.form-group select:valid {
    border-color: #51cf66;
}

.chat-main {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.messages-container {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #f8f9fa;
}

.welcome-message {
    text-align: center;
    color: #666;
    font-style: italic;
    margin-bottom: 20px;
}

.message {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-end;
}

.message.own {
    justify-content: flex-end;
}

.message-content {
    max-width: 70%;
    padding: 12px 18px;
    border-radius: 18px;
    position: relative;
    word-wrap: break-word;
}

.message.own .message-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom-right-radius: 5px;
}

.message:not(.own) .message-content {
    background: white;
    color: #333;
    border: 1px solid #e0e0e0;
    border-bottom-left-radius: 5px;
}

.message-info {
    font-size: 12px;
    opacity: 0.7;
    margin: 5px 10px 0;
}

.message.own .message-info {
    text-align: right;
}

.message-input-container {
    padding: 20px;
    background: white;
    border-top: 1px solid #e0e0e0;
}

.input-wrapper {
    display: flex;
    gap: 10px;
    align-items: center;
}

#messageInput {
    flex: 1;
    padding: 15px;
    border: 2px solid #e0e0e0;
    border-radius: 25px;
    font-size: 16px;
    font-family: 'Inter', sans-serif;
}

#messageInput:focus {
    outline: none;
    border-color: #667eea;
}

#sendBtn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.2s;
}

#sendBtn:hover {
    transform: scale(1.1);
}

.typing-indicator {
    font-size: 14px;
    color: #666;
    font-style: italic;
    margin-top: 10px;
    padding-right: 15px;
}

.user-joined, .user-left {
    text-align: center;
    color: #666;
    font-style: italic;
    margin: 10px 0;
    font-size: 14px;
}

@media (max-width: 768px) {
    body {
        padding: 10px;
    }

    .chat-container {
        height: calc(100vh - 20px);
        border-radius: 15px;
    }

    .setup-card {
        padding: 30px 20px;
    }

    .message-content {
        max-width: 85%;
    }
}
